#!/usr/bin/env node

/**
 * Demo script that shows the dashboard working with mock data
 * This simulates what the dashboard would look like with real API data
 * Run with: node demo-with-mock-data.js
 */

const express = require('express');
const path = require('path');

const app = express();
const PORT = 3001;

// Mock data that simulates real API responses
const mockData = {
  defender: {
    machines: [
      { hostname: 'DESKTOP-ABC123', id: 'def-001', osPlatform: 'Windows10', lastSeen: '2024-01-15T10:30:00Z' },
      { hostname: 'LAPTOP-XYZ789', id: 'def-002', osPlatform: 'Windows11', lastSeen: '2024-01-15T09:15:00Z' },
      { hostname: 'SERVER-PROD01', id: 'def-003', osPlatform: 'WindowsServer2022', lastSeen: '2024-01-15T11:00:00Z' },
      { hostname: 'MACBOOK-DEV', id: 'def-004', osPlatform: 'macOS', lastSeen: '2024-01-15T08:45:00Z' },
      { hostname: 'UBUNTU-BUILD', id: 'def-005', osPlatform: 'Linux', lastSeen: '2024-01-15T10:00:00Z' }
    ]
  },
  fleet: {
    hosts: [
      { hostname: 'DESKTOP-ABC123', id: 1, platform: 'windows', computer_name: 'DESKTOP-ABC123', status: 'online' },
      { hostname: 'LAPTOP-XYZ789', id: 2, platform: 'windows', computer_name: 'LAPTOP-XYZ789', status: 'online' },
      { hostname: 'SERVER-PROD01', id: 3, platform: 'windows', computer_name: 'SERVER-PROD01', status: 'online' },
      { hostname: 'LINUX-WEB01', id: 4, platform: 'ubuntu', computer_name: 'LINUX-WEB01', status: 'offline' }
    ]
  },
  tailscale: {
    devices: [
      { hostname: 'desktop-abc123', id: 'ts-001', name: 'desktop-abc123', online: true, os: 'windows' },
      { hostname: 'laptop-xyz789', id: 'ts-002', name: 'laptop-xyz789', online: true, os: 'windows' },
      { hostname: 'macbook-dev', id: 'ts-003', name: 'macbook-dev', online: true, os: 'macOS' },
      { hostname: 'mobile-phone', id: 'ts-004', name: 'mobile-phone', online: false, os: 'iOS' }
    ]
  }
};

// Normalize hostname function (same as in the real service)
function normalizeHostname(hostname) {
  if (!hostname) return '';
  
  let normalized = hostname.toLowerCase().trim();
  
  // Remove common domain suffixes
  const suffixes = ['.local', '.domain', '.corp', '.internal'];
  for (const suffix of suffixes) {
    if (normalized.endsWith(suffix)) {
      normalized = normalized.slice(0, -suffix.length);
      break;
    }
  }
  
  // Remove Tailscale domain suffixes
  if (normalized.includes('.ts.net')) {
    normalized = normalized.split('.')[0];
  }
  
  return normalized;
}

// Create unified view (same logic as real service)
function createUnifiedView() {
  const machineMap = new Map();

  // Process Defender machines
  mockData.defender.machines.forEach(machine => {
    const hostname = normalizeHostname(machine.hostname);
    if (hostname) {
      machineMap.set(hostname, {
        hostname: hostname,
        originalHostnames: { defender: machine.hostname },
        defender: { present: true, data: machine },
        fleet: { present: false, data: null },
        tailscale: { present: false, data: null },
        allPlatforms: false
      });
    }
  });

  // Process Fleet hosts
  mockData.fleet.hosts.forEach(host => {
    const hostname = normalizeHostname(host.hostname || host.computer_name);
    if (hostname) {
      if (machineMap.has(hostname)) {
        const machine = machineMap.get(hostname);
        machine.fleet.present = true;
        machine.fleet.data = host;
        machine.originalHostnames.fleet = host.hostname || host.computer_name;
      } else {
        machineMap.set(hostname, {
          hostname: hostname,
          originalHostnames: { fleet: host.hostname || host.computer_name },
          defender: { present: false, data: null },
          fleet: { present: true, data: host },
          tailscale: { present: false, data: null },
          allPlatforms: false
        });
      }
    }
  });

  // Process Tailscale devices
  mockData.tailscale.devices.forEach(device => {
    const hostname = normalizeHostname(device.hostname || device.name);
    if (hostname) {
      if (machineMap.has(hostname)) {
        const machine = machineMap.get(hostname);
        machine.tailscale.present = true;
        machine.tailscale.data = device;
        machine.originalHostnames.tailscale = device.hostname || device.name;
      } else {
        machineMap.set(hostname, {
          hostname: hostname,
          originalHostnames: { tailscale: device.hostname || device.name },
          defender: { present: false, data: null },
          fleet: { present: false, data: null },
          tailscale: { present: true, data: device },
          allPlatforms: false
        });
      }
    }
  });

  // Calculate allPlatforms status
  const unifiedMachines = Array.from(machineMap.values()).map(machine => {
    machine.allPlatforms = machine.defender.present && machine.fleet.present && machine.tailscale.present;
    return machine;
  });

  return unifiedMachines.sort((a, b) => a.hostname.localeCompare(b.hostname));
}

// Generate summary
function generateSummary(unified) {
  return {
    platforms: {
      defender: { total: mockData.defender.machines.length, status: 'success' },
      fleet: { total: mockData.fleet.hosts.length, status: 'success' },
      tailscale: { total: mockData.tailscale.devices.length, status: 'success' }
    },
    unified: {
      totalUniqueMachines: unified.length,
      inAllPlatforms: unified.filter(m => m.allPlatforms).length,
      inDefenderOnly: unified.filter(m => m.defender.present && !m.fleet.present && !m.tailscale.present).length,
      inFleetOnly: unified.filter(m => !m.defender.present && m.fleet.present && !m.tailscale.present).length,
      inTailscaleOnly: unified.filter(m => !m.defender.present && !m.fleet.present && m.tailscale.present).length,
      missingFromDefender: unified.filter(m => !m.defender.present).length,
      missingFromFleet: unified.filter(m => !m.fleet.present).length,
      missingFromTailscale: unified.filter(m => !m.tailscale.present).length
    }
  };
}

// Middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Mock API endpoints
app.get('/api/inventory/unified', (req, res) => {
  const unified = createUnifiedView();
  const summary = generateSummary(unified);
  
  res.json({
    success: true,
    data: unified,
    summary: summary,
    lastUpdated: new Date().toISOString(),
    timestamp: new Date().toISOString()
  });
});

app.get('/api/inventory/health', (req, res) => {
  res.json({
    success: true,
    data: {
      overall: 'healthy',
      platforms: {
        defender: { status: 'healthy', message: 'Microsoft Defender API is accessible', authenticated: true },
        fleet: { status: 'healthy', message: 'Fleet API is accessible', authenticated: true },
        tailscale: { status: 'healthy', message: 'Tailscale API is accessible', authenticated: true }
      },
      timestamp: new Date().toISOString()
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/inventory/config', (req, res) => {
  res.json({
    success: true,
    data: {
      platforms: {
        azure: { configured: true },
        fleet: { configured: true },
        tailscale: { configured: true }
      },
      allConfigured: true,
      configValid: true
    },
    timestamp: new Date().toISOString()
  });
});

// Serve the main page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(PORT, () => {
  console.log('🎭 Security Platform Dashboard - DEMO MODE');
  console.log('==========================================');
  console.log(`📊 Demo server running on port ${PORT}`);
  console.log(`🌐 Open http://localhost:${PORT} to view the demo`);
  console.log('');
  console.log('📋 Demo Data Summary:');
  console.log(`   • Defender: ${mockData.defender.machines.length} machines`);
  console.log(`   • Fleet: ${mockData.fleet.hosts.length} hosts`);
  console.log(`   • Tailscale: ${mockData.tailscale.devices.length} devices`);
  console.log('');
  console.log('✨ This demo shows how the dashboard works with real data!');
  console.log('💡 To use with real APIs, configure credentials and run: npm start');
});
