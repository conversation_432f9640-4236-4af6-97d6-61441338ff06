# Security Platform Dashboard

A unified web dashboard that displays machine inventory across three security platforms:
- **Microsoft Defender for Endpoint**
- **Fleet**
- **Tailscale**

## Features

- **Unified View**: 4-column table showing machine presence across all platforms
- **Visual Indicators**: Green checkmarks (✓) for present machines, Red X (✗) for missing
- **Real-time Data**: Fetch fresh data from all three APIs
- **Search & Filter**: Find machines by hostname and filter by platform presence
- **Health Monitoring**: Check API connectivity and authentication status
- **Responsive Design**: Works on desktop and mobile devices

## Prerequisites

- Node.js 16+ and npm
- API credentials for all three platforms:
  - Microsoft Defender: Azure AD app with appropriate permissions
  - Fleet: API token or username/password
  - Tailscale: API key and tailnet name

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure API credentials** (choose one method):

   **Option A: Environment Variables**
   ```bash
   export AZURE_TENANT_ID="your-tenant-id"
   export AZURE_CLIENT_ID="your-client-id"
   export AZURE_CLIENT_SECRET="your-client-secret"
   export FLEET_URL="https://fleet.blue-pollux.ts.net"
   export FLEET_API_TOKEN="your-fleet-api-token"
   export TAILSCALE_API_KEY="your-tailscale-api-key"
   export TAILSCALE_TAILNET="your-tailnet"
   ```

   **Option B: Configuration File**
   ```bash
   cp config.json.example config.json
   # Edit config.json with your credentials
   ```

   **Option C: .env File**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

## Configuration

### Microsoft Defender for Endpoint

1. Register an Azure AD application
2. Grant the following API permissions:
   - `WindowsDefenderATP.Machine.Read.All`
3. Create a client secret
4. Use the tenant ID, client ID, and client secret

### Fleet

1. **API Token Method** (Recommended):
   - Generate an API token in Fleet admin panel
   - Use the token in `FLEET_API_TOKEN`

2. **Username/Password Method**:
   - Use your Fleet username and password
   - Set `FLEET_USERNAME` and `FLEET_PASSWORD`

### Tailscale

1. Generate an API key in Tailscale admin console
2. Note your tailnet name (e.g., `example.ts.net` → use `example`)

## Usage

1. **Start the server**:
   ```bash
   npm start
   ```

2. **Open the dashboard**:
   ```
   http://localhost:3000
   ```

3. **Development mode** (with auto-reload):
   ```bash
   npm run dev
   ```

## API Endpoints

- `GET /api/inventory` - Full inventory data
- `GET /api/inventory/unified` - Unified machine list
- `GET /api/inventory/summary` - Summary statistics
- `GET /api/inventory/platforms/:platform` - Platform-specific data
- `POST /api/inventory/refresh` - Force data refresh
- `GET /api/inventory/health` - Platform health status
- `GET /api/inventory/search?q=hostname` - Search machines
- `GET /api/inventory/config` - Configuration status

## Dashboard Features

### Main Table
- **Hostname**: Machine hostname (normalized across platforms)
- **Defender**: ✓ if present in Microsoft Defender, ✗ if missing
- **Fleet**: ✓ if present in Fleet, ✗ if missing
- **Tailscale**: ✓ if present in Tailscale, ✗ if missing
- **All Platforms**: ✓ only if present in ALL three platforms

### Controls
- **Refresh Data**: Force refresh from all APIs
- **Health Check**: View API connectivity status
- **Search**: Filter machines by hostname
- **Filters**: 
  - Show only machines in all platforms
  - Show only machines missing from platforms

### Summary Cards
- Total unique machines across all platforms
- Machines present in all platforms
- Machines missing from each platform

## Troubleshooting

### Authentication Issues
1. Check API credentials in configuration
2. Use the Health Check feature to test connectivity
3. Verify API permissions and scopes

### Data Issues
1. Use the Refresh button to get fresh data
2. Check browser console for JavaScript errors
3. Verify API endpoints are accessible

### Performance
- Data is cached for 5 minutes by default
- Use filters to reduce displayed data
- Consider pagination for very large datasets

## Security Notes

- Store API credentials securely
- Use environment variables in production
- Consider using Azure Key Vault or similar for secrets
- The dashboard runs on HTTP by default - use HTTPS in production

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API    │    │   External APIs │
│   (HTML/CSS/JS) │◄──►│   (Express.js)   │◄──►│   - Defender    │
│                 │    │                  │    │   - Fleet       │
│   - Dashboard   │    │   - Inventory    │    │   - Tailscale   │
│   - Search      │    │   - Health       │    │                 │
│   - Filters     │    │   - Config       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## License

MIT License - see LICENSE file for details
