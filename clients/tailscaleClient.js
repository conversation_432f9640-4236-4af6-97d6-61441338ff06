const axios = require('axios');

/**
 * Tailscale API Client
 * Handles authentication and device retrieval from Tailscale
 */
class TailscaleClient {
  constructor(config) {
    this.apiKey = config.api_key;
    this.tailnet = config.tailnet;
    this.baseUrl = 'https://api.tailscale.com';
  }

  /**
   * Get headers for API requests
   * @returns {Object} Request headers
   */
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Get list of devices from Tailscale
   * @returns {Promise<Array>} Array of device objects
   */
  async getDevices() {
    if (!this.apiKey || !this.tailnet) {
      throw new Error('Tailscale API key and tailnet are required');
    }

    const url = `${this.baseUrl}/api/v2/tailnet/${this.tailnet}/devices`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      const devices = response.data.devices || [];
      console.log(`✓ Retrieved ${devices.length} devices from Tailscale`);
      
      // Normalize device data
      return devices.map(device => ({
        id: device.id,
        hostname: device.hostname,
        name: device.name,
        computerName: device.hostname,
        addresses: device.addresses,
        os: device.os,
        clientVersion: device.clientVersion,
        lastSeen: device.lastSeen,
        online: device.online,
        blocked: device.blocked,
        keyExpiryDisabled: device.keyExpiryDisabled,
        machineKey: device.machineKey,
        nodeKey: device.nodeKey,
        user: device.user,
        tags: device.tags,
        source: 'tailscale',
        rawData: device
      }));
      
    } catch (error) {
      console.error('✗ Failed to get devices from Tailscale:', error.response?.data || error.message);
      throw new Error(`Tailscale API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get device details by ID
   * @param {string} deviceId - Device ID
   * @returns {Promise<Object>} Device details
   */
  async getDeviceById(deviceId) {
    if (!this.apiKey || !this.tailnet) {
      throw new Error('Tailscale API key and tailnet are required');
    }

    const url = `${this.baseUrl}/api/v2/device/${deviceId}`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data;
      
    } catch (error) {
      console.error(`✗ Failed to get device ${deviceId} from Tailscale:`, error.response?.data || error.message);
      throw new Error(`Tailscale API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Search for devices by hostname
   * @param {string} hostname - Hostname to search for
   * @returns {Promise<Array>} Array of matching devices
   */
  async findDevicesByHostname(hostname) {
    const devices = await this.getDevices();
    return devices.filter(device => {
      const deviceHostname = device.hostname || device.name || '';
      return deviceHostname.toLowerCase() === hostname.toLowerCase();
    });
  }

  /**
   * Get tailnet information
   * @returns {Promise<Object>} Tailnet details
   */
  async getTailnetInfo() {
    if (!this.apiKey || !this.tailnet) {
      throw new Error('Tailscale API key and tailnet are required');
    }

    const url = `${this.baseUrl}/api/v2/tailnet/${this.tailnet}`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data;
      
    } catch (error) {
      console.error('✗ Failed to get tailnet info from Tailscale:', error.response?.data || error.message);
      throw new Error(`Tailscale API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get device routes
   * @param {string} deviceId - Device ID
   * @returns {Promise<Array>} Array of routes
   */
  async getDeviceRoutes(deviceId) {
    if (!this.apiKey || !this.tailnet) {
      throw new Error('Tailscale API key and tailnet are required');
    }

    const url = `${this.baseUrl}/api/v2/device/${deviceId}/routes`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data.routes || [];
      
    } catch (error) {
      console.error(`✗ Failed to get routes for device ${deviceId} from Tailscale:`, error.response?.data || error.message);
      throw new Error(`Tailscale API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get ACL (Access Control List) for the tailnet
   * @returns {Promise<Object>} ACL configuration
   */
  async getACL() {
    if (!this.apiKey || !this.tailnet) {
      throw new Error('Tailscale API key and tailnet are required');
    }

    const url = `${this.baseUrl}/api/v2/tailnet/${this.tailnet}/acl`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data;
      
    } catch (error) {
      console.error('✗ Failed to get ACL from Tailscale:', error.response?.data || error.message);
      throw new Error(`Tailscale API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get health status of the client
   * @returns {Promise<Object>} Health status
   */
  async getHealthStatus() {
    try {
      if (!this.apiKey || !this.tailnet) {
        return {
          status: 'error',
          message: 'API key and tailnet are required',
          authenticated: false
        };
      }

      // Test API connectivity by getting tailnet info
      await this.getTailnetInfo();

      return {
        status: 'healthy',
        message: 'Tailscale API is accessible',
        authenticated: true,
        tailnet: this.tailnet,
        lastChecked: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        authenticated: !!this.apiKey,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Get device statistics
   * @returns {Promise<Object>} Device statistics
   */
  async getDeviceStats() {
    try {
      const devices = await this.getDevices();
      
      const stats = {
        total: devices.length,
        online: devices.filter(d => d.online).length,
        offline: devices.filter(d => !d.online).length,
        blocked: devices.filter(d => d.blocked).length,
        byOS: {}
      };

      // Group by OS
      devices.forEach(device => {
        const os = device.os || 'unknown';
        stats.byOS[os] = (stats.byOS[os] || 0) + 1;
      });

      return stats;
      
    } catch (error) {
      console.error('✗ Failed to get device statistics from Tailscale:', error.message);
      throw error;
    }
  }

  /**
   * Normalize hostname for comparison
   * Tailscale hostnames might have different formats
   * @param {string} hostname - Original hostname
   * @returns {string} Normalized hostname
   */
  normalizeHostname(hostname) {
    if (!hostname) return '';
    
    // Remove tailscale domain suffix if present
    const tailscaleSuffix = `.${this.tailnet}.ts.net`;
    if (hostname.endsWith(tailscaleSuffix)) {
      hostname = hostname.slice(0, -tailscaleSuffix.length);
    }
    
    // Remove .ts.net suffix
    if (hostname.endsWith('.ts.net')) {
      hostname = hostname.slice(0, -7);
    }
    
    return hostname.toLowerCase();
  }
}

module.exports = TailscaleClient;
