const axios = require('axios');

/**
 * Fleet API Client
 * Handles authentication and host retrieval from Fleet
 * Based on the fleetctl command pattern from reference_code.py
 */
class FleetClient {
  constructor(config) {
    this.baseUrl = config.url;
    this.apiToken = config.api_token;
    this.username = config.username;
    this.password = config.password;
    this.sessionToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Authenticate with Fleet API using API token or username/password
   * @returns {Promise<boolean>} Success status
   */
  async authenticate() {
    if (this.apiToken) {
      // Use API token authentication
      console.log('✓ Using Fleet API token authentication');
      return true;
    }

    if (this.username && this.password) {
      // Use username/password authentication
      const loginUrl = `${this.baseUrl}/api/v1/fleet/login`;
      
      try {
        const response = await axios.post(loginUrl, {
          email: this.username,
          password: this.password
        }, {
          timeout: 30000
        });

        this.sessionToken = response.data.token;
        // Fleet tokens typically last 24 hours, set expiry to 23 hours for safety
        this.tokenExpiry = Date.now() + (23 * 60 * 60 * 1000);
        
        console.log('✓ Fleet username/password authentication successful');
        return true;
        
      } catch (error) {
        console.error('✗ Fleet authentication failed:', error.response?.data || error.message);
        return false;
      }
    }

    console.error('✗ No Fleet authentication method configured');
    return false;
  }

  /**
   * Check if current session is valid and refresh if needed
   * @returns {Promise<boolean>} Success status
   */
  async ensureAuthenticated() {
    if (this.apiToken) {
      return true; // API tokens don't expire
    }

    if (!this.sessionToken || !this.tokenExpiry || Date.now() >= this.tokenExpiry) {
      return await this.authenticate();
    }
    return true;
  }

  /**
   * Get headers for API requests
   * @returns {Object} Request headers
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.apiToken) {
      headers['Authorization'] = `Bearer ${this.apiToken}`;
    } else if (this.sessionToken) {
      headers['Authorization'] = `Bearer ${this.sessionToken}`;
    }

    return headers;
  }

  /**
   * Get list of hosts from Fleet
   * @param {number} teamId - Team ID (default: 0 for global)
   * @returns {Promise<Array>} Array of host objects
   */
  async getHosts(teamId = 0) {
    if (!await this.ensureAuthenticated()) {
      throw new Error('Failed to authenticate with Fleet');
    }

    const url = `${this.baseUrl}/api/v1/fleet/hosts`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        params: {
          team_id: teamId,
          per_page: 1000 // Get up to 1000 hosts per request
        },
        timeout: 30000
      });

      const hosts = response.data.hosts || [];
      console.log(`✓ Retrieved ${hosts.length} hosts from Fleet`);
      
      // Normalize host data
      return hosts.map(host => ({
        id: host.id,
        hostname: host.hostname,
        computerName: host.computer_name || host.hostname,
        displayName: host.display_name,
        platform: host.platform,
        osVersion: host.os_version,
        lastSeen: host.seen_time,
        status: host.status,
        teamId: host.team_id,
        uuid: host.uuid,
        hardwareSerial: host.hardware_serial,
        source: 'fleet',
        rawData: host
      }));
      
    } catch (error) {
      console.error('✗ Failed to get hosts from Fleet:', error.response?.data || error.message);
      throw new Error(`Fleet API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get host details by ID
   * @param {number} hostId - Host ID
   * @returns {Promise<Object>} Host details
   */
  async getHostById(hostId) {
    if (!await this.ensureAuthenticated()) {
      throw new Error('Failed to authenticate with Fleet');
    }

    const url = `${this.baseUrl}/api/v1/fleet/hosts/${hostId}`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data.host;
      
    } catch (error) {
      console.error(`✗ Failed to get host ${hostId} from Fleet:`, error.response?.data || error.message);
      throw new Error(`Fleet API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Search for hosts by hostname
   * @param {string} hostname - Hostname to search for
   * @returns {Promise<Array>} Array of matching hosts
   */
  async findHostsByHostname(hostname) {
    const hosts = await this.getHosts();
    return hosts.filter(host => {
      const hostHostname = host.hostname || host.computerName || '';
      return hostHostname.toLowerCase() === hostname.toLowerCase();
    });
  }

  /**
   * Get teams from Fleet
   * @returns {Promise<Array>} Array of team objects
   */
  async getTeams() {
    if (!await this.ensureAuthenticated()) {
      throw new Error('Failed to authenticate with Fleet');
    }

    const url = `${this.baseUrl}/api/v1/fleet/teams`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data.teams || [];
      
    } catch (error) {
      console.error('✗ Failed to get teams from Fleet:', error.response?.data || error.message);
      throw new Error(`Fleet API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get health status of the client
   * @returns {Promise<Object>} Health status
   */
  async getHealthStatus() {
    try {
      const isAuthenticated = await this.ensureAuthenticated();
      
      if (!isAuthenticated) {
        return {
          status: 'error',
          message: 'Authentication failed',
          authenticated: false
        };
      }

      // Test API connectivity by making a simple request
      const url = `${this.baseUrl}/api/v1/fleet/hosts`;
      await axios.get(url, {
        headers: this.getHeaders(),
        params: { per_page: 1 }, // Only get 1 host for health check
        timeout: 10000
      });

      return {
        status: 'healthy',
        message: 'Fleet API is accessible',
        authenticated: true,
        authMethod: this.apiToken ? 'api_token' : 'username_password',
        lastChecked: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        authenticated: !!this.sessionToken || !!this.apiToken,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Simulate fleetctl get h --json --team 0 command
   * This matches the command used in reference_code.py
   * @returns {Promise<Array>} Array of host objects in fleetctl format
   */
  async getHostsFleetctlFormat(teamId = 0) {
    const hosts = await this.getHosts(teamId);
    
    // Convert to format similar to fleetctl output
    return hosts.map(host => ({
      computer_name: host.computerName,
      hostname: host.hostname,
      server_url: `${this.baseUrl}/api/mdm/microsoft/discovery`,
      spec: {
        mdm: {
          name: 'Fleet'
        }
      },
      ...host.rawData
    }));
  }
}

module.exports = FleetClient;
