const axios = require('axios');

/**
 * Microsoft Defender for Endpoint API Client
 * Handles authentication and machine retrieval from Microsoft Defender
 * Based on the authentication pattern from deploy_fleet.py
 */
class DefenderClient {
  constructor(config) {
    this.tenantId = config.tenant_id;
    this.clientId = config.client_id;
    this.clientSecret = config.client_secret;
    this.accessToken = null;
    this.tokenExpiry = null;
    this.baseUrl = 'https://api.securitycenter.microsoft.com';
  }

  /**
   * Authenticate with Microsoft Graph API to get access token
   * @returns {Promise<boolean>} Success status
   */
  async authenticate() {
    const authUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    
    const authData = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: this.clientId,
      client_secret: this.clientSecret,
      scope: 'https://api.securitycenter.microsoft.com/.default'
    });

    try {
      const response = await axios.post(authUrl, authData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      
      // Calculate token expiry (subtract 5 minutes for safety)
      const expiresIn = tokenData.expires_in || 3600;
      this.tokenExpiry = Date.now() + ((expiresIn - 300) * 1000);
      
      console.log('✓ Microsoft Defender authentication successful');
      return true;
      
    } catch (error) {
      console.error('✗ Microsoft Defender authentication failed:', error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Check if current token is valid and refresh if needed
   * @returns {Promise<boolean>} Success status
   */
  async ensureAuthenticated() {
    if (!this.accessToken || !this.tokenExpiry || Date.now() >= this.tokenExpiry) {
      return await this.authenticate();
    }
    return true;
  }

  /**
   * Get headers for API requests
   * @returns {Object} Request headers
   */
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Get list of machines from Microsoft Defender
   * @returns {Promise<Array>} Array of machine objects
   */
  async getMachines() {
    if (!await this.ensureAuthenticated()) {
      throw new Error('Failed to authenticate with Microsoft Defender');
    }

    const url = `${this.baseUrl}/api/machines`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000 // 30 second timeout
      });

      const machines = response.data.value || [];
      console.log(`✓ Retrieved ${machines.length} machines from Microsoft Defender`);
      
      // Normalize machine data
      return machines.map(machine => ({
        id: machine.id,
        hostname: machine.computerDnsName,
        computerName: machine.computerDnsName,
        osPlatform: machine.osPlatform,
        osVersion: machine.osVersion,
        lastSeen: machine.lastSeen,
        healthStatus: machine.healthStatus,
        riskScore: machine.riskScore,
        exposureLevel: machine.exposureLevel,
        source: 'defender',
        rawData: machine
      }));
      
    } catch (error) {
      console.error('✗ Failed to get machines from Microsoft Defender:', error.response?.data || error.message);
      throw new Error(`Microsoft Defender API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Get machine details by ID
   * @param {string} machineId - Machine ID
   * @returns {Promise<Object>} Machine details
   */
  async getMachineById(machineId) {
    if (!await this.ensureAuthenticated()) {
      throw new Error('Failed to authenticate with Microsoft Defender');
    }

    const url = `${this.baseUrl}/api/machines/${machineId}`;
    
    try {
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data;
      
    } catch (error) {
      console.error(`✗ Failed to get machine ${machineId} from Microsoft Defender:`, error.response?.data || error.message);
      throw new Error(`Microsoft Defender API error: ${error.response?.status || error.message}`);
    }
  }

  /**
   * Search for machines by hostname
   * @param {string} hostname - Hostname to search for
   * @returns {Promise<Array>} Array of matching machines
   */
  async findMachinesByHostname(hostname) {
    const machines = await this.getMachines();
    return machines.filter(machine => 
      machine.hostname && machine.hostname.toLowerCase() === hostname.toLowerCase()
    );
  }

  /**
   * Get health status of the client
   * @returns {Promise<Object>} Health status
   */
  async getHealthStatus() {
    try {
      const isAuthenticated = await this.ensureAuthenticated();
      
      if (!isAuthenticated) {
        return {
          status: 'error',
          message: 'Authentication failed',
          authenticated: false
        };
      }

      // Test API connectivity by making a simple request
      const url = `${this.baseUrl}/api/machines`;
      await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 10000,
        params: { '$top': 1 } // Only get 1 machine for health check
      });

      return {
        status: 'healthy',
        message: 'Microsoft Defender API is accessible',
        authenticated: true,
        lastChecked: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        authenticated: !!this.accessToken,
        lastChecked: new Date().toISOString()
      };
    }
  }
}

module.exports = DefenderClient;
