#!/usr/bin/env node

/**
 * Simple API test script to verify the dashboard is working
 * Run with: node test-api.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testEndpoint(endpoint, description) {
    try {
        console.log(`\n🧪 Testing: ${description}`);
        console.log(`📡 GET ${endpoint}`);
        
        const response = await axios.get(`${BASE_URL}${endpoint}`);
        
        console.log(`✅ Status: ${response.status}`);
        console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
        
        return true;
    } catch (error) {
        console.log(`❌ Error: ${error.response?.status || error.code}`);
        console.log(`📝 Message: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

async function runTests() {
    console.log('🚀 Security Platform Dashboard API Tests');
    console.log('==========================================');
    
    const tests = [
        {
            endpoint: '/api/inventory/config',
            description: 'Configuration Status'
        },
        {
            endpoint: '/api/inventory/health',
            description: 'Platform Health Check'
        },
        {
            endpoint: '/api/inventory/summary',
            description: 'Inventory Summary'
        },
        {
            endpoint: '/api/inventory/unified',
            description: 'Unified Machine List'
        },
        {
            endpoint: '/api/inventory/platforms/defender',
            description: 'Defender Platform Data'
        },
        {
            endpoint: '/api/inventory/platforms/fleet',
            description: 'Fleet Platform Data'
        },
        {
            endpoint: '/api/inventory/platforms/tailscale',
            description: 'Tailscale Platform Data'
        },
        {
            endpoint: '/api/inventory/search?q=test',
            description: 'Search Functionality'
        }
    ];
    
    let passed = 0;
    let total = tests.length;
    
    for (const test of tests) {
        const success = await testEndpoint(test.endpoint, test.description);
        if (success) passed++;
    }
    
    console.log('\n📋 Test Results');
    console.log('================');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${total - passed}/${total}`);
    
    if (passed === total) {
        console.log('\n🎉 All tests passed! The API is working correctly.');
    } else {
        console.log('\n⚠️  Some tests failed. Check the output above for details.');
    }
    
    console.log('\n💡 Next Steps:');
    console.log('1. Configure API credentials (see SETUP.md)');
    console.log('2. Open http://localhost:3000 in your browser');
    console.log('3. Use the Health Check button to verify platform connectivity');
}

// Check if server is running
async function checkServer() {
    try {
        await axios.get(BASE_URL);
        return true;
    } catch (error) {
        console.log('❌ Server is not running!');
        console.log('💡 Start the server with: npm start');
        return false;
    }
}

async function main() {
    const serverRunning = await checkServer();
    if (serverRunning) {
        await runTests();
    }
}

main().catch(console.error);
