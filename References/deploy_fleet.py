#!/usr/bin/env python3
"""
Microsoft Live Incident Response API Fleet Deployment Script

This script deploys Fleet to Windows machines using Microsoft's Live Incident Response API.
It automatically queues two tasks:
1. Deploy "fleet-osquery-EXTERNAL 1.msi" from the Library
2. Run "Defender-Install-Fleet.ps1" from the library

Currently supports Windows machines only. Future versions will support:
- Windows: .msi installer + PowerShell script
- Linux/Debian/Ubuntu: .deb package + bash script
- macOS: .pkg installer + bash script

Usage:
    python deploy_fleet.py [machine_name]
    
If no machine name is provided, it will target Windows machines that don't begin with "MWS-" or "WS-"
"""

import requests
import json
import sys
import argparse
from typing import List, Dict, Optional
import time
import logging
from datetime import datetime

class LiveResponseClient:
    def __init__(self, tenant_id: str, client_id: str, client_secret: str):
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.base_url = "https://api.securitycenter.microsoft.com"
        
    def authenticate(self) -> bool:
        """Authenticate with Microsoft Graph API to get access token"""
        auth_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        auth_data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://api.securitycenter.microsoft.com/.default'
        }
        
        try:
            response = requests.post(auth_url, data=auth_data)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data['access_token']
            print("✓ Authentication successful")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Authentication failed: {e}")
            return False
    
    def get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
    
    def get_devices(self) -> List[Dict]:
        """Get list of devices from the inventory"""
        url = f"{self.base_url}/api/machines"
        
        try:
            response = requests.get(url, headers=self.get_headers())
            response.raise_for_status()
            
            data = response.json()
            return data.get('value', [])
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Failed to get devices: {e}")
            return []
    
    def find_target_machines(self, machine_name: Optional[str] = None) -> List[Dict]:
        """Find target machines based on criteria"""
        devices = self.get_devices()
        
        if machine_name:
            # Look for specific machine
            target_machines = [device for device in devices 
                             if device.get('computerDnsName', '') and 
                                device.get('computerDnsName', '').lower() == machine_name.lower()]
            if not target_machines:
                print(f"✗ Machine '{machine_name}' not found")
            # Filter for Windows machines only
            target_machines = [device for device in target_machines 
                             if self._is_windows_machine(device)]
            if not target_machines:
                print(f"✗ Machine '{machine_name}' found but is not a Windows machine")
            return target_machines
        else:
            # Find Windows machines that don't start with "MWS-" or "WS-"
            target_machines = []
            for device in devices:
                hostname = device.get('computerDnsName', '')
                if (hostname and 
                    not (hostname.upper().startswith('MWS-') or hostname.upper().startswith('WS-')) and
                    self._is_windows_machine(device)):
                    target_machines.append(device)
            
            print(f"Found {len(target_machines)} eligible Windows machines (excluding MWS-* and WS-* hostnames)")
            return target_machines
    
    def _is_windows_machine(self, device: Dict) -> bool:
        """Check if a device is running Windows"""
        os_platform = device.get('osPlatform', '').lower()
        return 'windows' in os_platform
    
    def _get_deployment_config(self, device: Dict) -> Dict[str, str]:
        """Get deployment configuration based on OS (prepared for future multi-OS support)"""
        os_platform = device.get('osPlatform', '').lower()
        
        # Current implementation: Windows only
        if 'windows' in os_platform:
            return {
                'installer_file': 'fleet-osquery-EXTERNAL 1.msi',
                'install_script': 'Defender-Install-Fleet.ps1',
                'os_type': 'Windows'
            }
        
        # Future multi-OS support structure:
        # elif 'linux' in os_platform or 'ubuntu' in os_platform or 'debian' in os_platform:
        #     return {
        #         'installer_file': 'fleet-osquery-EXTERNAL.deb',
        #         'install_script': 'Install-Fleet.sh',
        #         'os_type': 'Linux'
        #     }
        # elif 'macos' in os_platform or 'darwin' in os_platform:
        #     return {
        #         'installer_file': 'fleet-osquery-EXTERNAL.pkg',
        #         'install_script': 'Install-Fleet.sh',
        #         'os_type': 'macOS'
        #     }
        
        # Default fallback (should not reach here with current filtering)
        return {
            'installer_file': 'fleet-osquery-EXTERNAL 1.msi',
            'install_script': 'Defender-Install-Fleet.ps1',
            'os_type': 'Unknown'
        }
    
    def create_machine_action(self, machine_id: str, action_type: str, **kwargs) -> Optional[str]:
        """Create a machine action using the correct API endpoint"""
        
        # Use the specific endpoint for the action type
        if action_type == "PutFile":
            url = f"{self.base_url}/api/machines/{machine_id}/runliveresponse"
            payload = {
                "Commands": [
                    {
                        "type": "PutFile",
                        "params": [
                            {
                                "key": "FileName",
                                "value": kwargs.get('FileName', '')
                            }
                        ]
                    }
                ],
                "Comment": kwargs.get('Comment', 'Fleet deployment')
            }
        elif action_type == "RunScript":
            url = f"{self.base_url}/api/machines/{machine_id}/runliveresponse"
            payload = {
                "Commands": [
                    {
                        "type": "RunScript",
                        "params": [
                            {
                                "key": "ScriptName",
                                "value": kwargs.get('ScriptName', '')
                            }
                        ]
                    }
                ],
                "Comment": kwargs.get('Comment', 'Fleet deployment')
            }
        else:
            print(f"✗ Unsupported action type: {action_type}")
            return None
        
        try:
            response = requests.post(url, headers=self.get_headers(), json=payload)
            response.raise_for_status()
            
            action_data = response.json()
            action_id = action_data.get('id')
            print(f"✓ Machine action created: {action_id}")
            return action_id
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Failed to create machine action: {e}")
            # Print more detailed error information
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_details = e.response.json()
                    print(f"✗ Error details: {json.dumps(error_details, indent=2)}")
                except:
                    print(f"✗ Response content: {e.response.text}")
            return None
    
    def wait_for_action_completion(self, action_id: str, timeout: int = 300, max_retries: int = 5) -> bool:
        """Wait for a machine action to complete with retry limit"""
        url = f"{self.base_url}/api/machineactions/{action_id}"
        start_time = time.time()
        retry_count = 0
        consecutive_errors = 0
        
        print(f"Waiting for action {action_id} to complete...")
        
        while time.time() - start_time < timeout and retry_count < max_retries:
            try:
                response = requests.get(url, headers=self.get_headers())
                response.raise_for_status()
                
                action_data = response.json()
                status = action_data.get('status', 'Unknown')
                
                print(f"Action status: {status}")
                
                # Reset consecutive errors on successful API call
                consecutive_errors = 0
                
                if status in ['Succeeded', 'Failed', 'Cancelled']:
                    if status == 'Succeeded':
                        print(f"✓ Action {action_id} completed successfully")
                        return True
                    else:
                        print(f"✗ Action {action_id} failed with status: {status}")
                        return False
                
                # Wait before checking again
                time.sleep(10)
                retry_count += 1
                
            except requests.exceptions.RequestException as e:
                consecutive_errors += 1
                print(f"Error checking action status (attempt {consecutive_errors}): {e}")
                
                # If we get 3 consecutive errors, give up on this action
                if consecutive_errors >= 3:
                    print(f"✗ Too many consecutive errors checking action {action_id}, giving up")
                    return False
                
                time.sleep(10)
                retry_count += 1
        
        if retry_count >= max_retries:
            print(f"✗ Maximum retries ({max_retries}) reached for action {action_id}")
        else:
            print(f"✗ Timeout waiting for action {action_id} to complete")
        
        return False
    
    def deploy_library_file(self, machine_id: str, filename: str) -> bool:
        """Deploy a file from the library to a machine"""
        action_id = self.create_machine_action(
            machine_id=machine_id,
            action_type="PutFile",
            Comment=f"Deploying {filename} for Fleet installation",
            FileName=filename
        )
        
        if action_id:
            print(f"✓ Queued deployment of {filename}")
            # Wait for the action to complete before returning
            return self.wait_for_action_completion(action_id)
        else:
            print(f"✗ Failed to deploy {filename}")
            return False
    
    def run_script_from_library(self, machine_id: str, script_name: str) -> bool:
        """Run a PowerShell script from the library on a machine"""
        action_id = self.create_machine_action(
            machine_id=machine_id,
            action_type="RunScript",
            Comment=f"Running {script_name} for Fleet installation",
            ScriptName=script_name
        )
        
        if action_id:
            print(f"✓ Queued execution of {script_name}")
            # Wait for the action to complete before returning
            return self.wait_for_action_completion(action_id)
        else:
            print(f"✗ Failed to run {script_name}")
            return False
    
    def debug_machine_capabilities(self, machine_id: str) -> Dict:
        """Debug method to check machine capabilities and available actions"""
        print(f"\n=== Debugging Machine Capabilities ===")
        
        # Check machine details
        machine_url = f"{self.base_url}/api/machines/{machine_id}"
        try:
            response = requests.get(machine_url, headers=self.get_headers())
            response.raise_for_status()
            machine_data = response.json()
            print(f"Machine details: {json.dumps(machine_data, indent=2)}")
        except Exception as e:
            print(f"Failed to get machine details: {e}")
        
        # Check available machine actions
        actions_url = f"{self.base_url}/api/machines/{machine_id}/machineactions"
        try:
            response = requests.get(actions_url, headers=self.get_headers())
            print(f"Machine actions endpoint status: {response.status_code}")
            if response.status_code == 200:
                actions_data = response.json()
                print(f"Available actions: {json.dumps(actions_data, indent=2)}")
            else:
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"Failed to get machine actions: {e}")
        
        # Test different API endpoints
        test_endpoints = [
            f"/api/machines/{machine_id}/collectinvestigationpackage",
            f"/api/machines/{machine_id}/antivirus/scan",
            f"/api/machines/{machine_id}/isolate",
            f"/api/machines/{machine_id}/runliveresponse"
        ]
        
        for endpoint in test_endpoints:
            url = f"{self.base_url}{endpoint}"
            try:
                # Use HEAD request to check if endpoint exists
                response = requests.head(url, headers=self.get_headers())
                print(f"Endpoint {endpoint}: Status {response.status_code}")
            except Exception as e:
                print(f"Endpoint {endpoint}: Error {e}")
        
        return {}
    
    def deploy_fleet_to_machine(self, machine: Dict) -> bool:
        """Deploy Fleet to a specific machine"""
        machine_id = machine.get('id')
        machine_name = machine.get('computerDnsName', 'Unknown')
        os_platform = machine.get('osPlatform', 'Unknown OS')
        
        # Get deployment configuration based on OS
        deploy_config = self._get_deployment_config(machine)
        
        print(f"\n--- Deploying Fleet to {machine_name} ({os_platform}) ---")
        print(f"Using {deploy_config['os_type']} deployment configuration")
        
        try:
            # Task 1: Deploy installer from library
            print(f"Task 1: Deploying {deploy_config['installer_file']}...")
            if not self.deploy_library_file(machine_id, deploy_config['installer_file']):
                self._log_failed_deployment(machine_name, machine_id, "Task 1 (MSI deployment) failed")
                return False
            
            print(f"Task 1 completed. Starting Task 2...")
            
            # Task 2: Run installation script from library
            print(f"Task 2: Running {deploy_config['install_script']}...")
            if not self.run_script_from_library(machine_id, deploy_config['install_script']):
                self._log_failed_deployment(machine_name, machine_id, "Task 2 (PowerShell script) failed")
                return False
            
            print(f"✓ Fleet deployment completed successfully for {machine_name}")
            return True
            
        except Exception as e:
            error_msg = f"Unexpected error during deployment: {str(e)}"
            print(f"✗ {error_msg}")
            self._log_failed_deployment(machine_name, machine_id, error_msg)
            return False
    
    def _log_failed_deployment(self, machine_name: str, machine_id: str, reason: str) -> None:
        """Log failed deployment to failed-deployments.log"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "machine_name": machine_name,
            "machine_id": machine_id,
            "reason": reason
        }
        
        try:
            with open('failed-deployments.log', 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
            print(f"✗ Deployment failure logged for {machine_name}")
        except Exception as e:
            print(f"✗ Failed to write to log file: {e}")

def load_config() -> Dict[str, str]:
    """Load configuration from environment variables or config file"""
    import os
    
    config = {
        'tenant_id': os.getenv('AZURE_TENANT_ID'),
        'client_id': os.getenv('AZURE_CLIENT_ID'),
        'client_secret': os.getenv('AZURE_CLIENT_SECRET')
    }
    
    # Try to load from config file if environment variables are not set
    if not all(config.values()):
        try:
            with open('config.json', 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except FileNotFoundError:
            pass
    
    return config

def main():
    parser = argparse.ArgumentParser(description='Deploy Fleet using Microsoft Live Incident Response API')
    parser.add_argument('machine_name', nargs='?', help='Target machine name (optional)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without executing')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode to inspect API capabilities')
    parser.add_argument('--config', help='Path to configuration file')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = load_config()
    
    # Validate configuration
    required_keys = ['tenant_id', 'client_id', 'client_secret']
    missing_keys = [key for key in required_keys if not config.get(key)]
    
    if missing_keys:
        print("✗ Missing required configuration:")
        for key in missing_keys:
            env_var = f"AZURE_{key.upper()}"
            print(f"  - {key}: Set {env_var} environment variable or add to config.json")
        print("\nExample config.json:")
        print(json.dumps({
            "tenant_id": "your-tenant-id",
            "client_id": "your-client-id", 
            "client_secret": "your-client-secret"
        }, indent=2))
        sys.exit(1)
    
    # Initialize client
    client = LiveResponseClient(
        tenant_id=config['tenant_id'],
        client_id=config['client_id'],
        client_secret=config['client_secret']
    )
    
    # Authenticate
    if not client.authenticate():
        sys.exit(1)
    
    # Find target machines
    target_machines = client.find_target_machines(args.machine_name)
    
    if not target_machines:
        print("✗ No target machines found")
        sys.exit(1)
    
    print(f"\nTarget machines ({len(target_machines)}):")
    for machine in target_machines:
        name = machine.get('computerDnsName', 'Unknown')
        os_info = machine.get('osPlatform', 'Unknown OS')
        print(f"  - {name} ({os_info})")
    
    if args.dry_run:
        print("\n[DRY RUN] Would deploy Fleet to the above machines")
        sys.exit(0)
    
    # Confirm deployment
    if len(target_machines) > 1:
        response = input(f"\nDeploy Fleet to {len(target_machines)} machines? (y/N): ")
        if response.lower() != 'y':
            print("Deployment cancelled")
            sys.exit(0)
    
    # Deploy Fleet to each machine
    success_count = 0
    for machine in target_machines:
        if args.debug:
            # Run debug mode first
            machine_id = machine.get('id')
            client.debug_machine_capabilities(machine_id)
        
        if client.deploy_fleet_to_machine(machine):
            success_count += 1
        time.sleep(2)  # Brief pause between machines
    
    print(f"\n=== Deployment Summary ===")
    print(f"Successfully queued Fleet deployment on {success_count}/{len(target_machines)} machines")
    
    if success_count > 0:
        print("\nNote: Tasks have been queued. Check the Microsoft Defender portal for execution status.")

if __name__ == "__main__":
    main()
