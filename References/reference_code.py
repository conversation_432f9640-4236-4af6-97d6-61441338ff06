#!/usr/bin/env python3

import subprocess
import json
import os
import logging
from datetime import datetime

# Path to the deploy_fleet script
DEPLOY_FLEET_PATH = './deploy_fleet.py'


def execute_command(command):
    """Utility function to execute shell commands"""
    logging.info(f"Executing command: {command}")
    try:
        result = subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        logging.debug(f"Command output: {result.stdout.decode('utf-8')}")
        return result.stdout.decode('utf-8')
    except subprocess.CalledProcessError as e:
        logging.error(f"Command '{command}' failed with error: {e.stderr.decode('utf-8')}")
        raise RuntimeError(f"Command '{command}' failed: {e.stderr.decode('utf-8')}")


def get_fleet_data():
    """Fetch JSON data from fleetctl command"""
    command = 'fleetctl get h --json --team 0'
    logging.info("Fetching fleet data using fleetctl...")
    output = execute_command(command)
    logging.debug(f"Raw output from fleetctl:\n{output}")

    # Split output into individual JSON objects
    raw_objects = output.strip().split('\n')  # Assuming each JSON object is on a new line

    # Parse each JSON object and gather into a list
    fleet_data = [json.loads(obj) for obj in raw_objects if obj.strip()]
    logging.info(f"Parsed {len(fleet_data)} fleet data objects.")

    return fleet_data

def get_defender_devices(client):
    """Fetch device data from Defender"""
    logging.info("Fetching devices from Defender...")
    if client.authenticate():
        devices = client.get_devices()
        logging.info(f"Retrieved {len(devices)} devices from Defender.")
        return devices
    logging.error("Failed to authenticate with Defender.")
    return []


def deploy_to_machine(machine_name):
    """Execute the deploy fleet script for a given machine"""
    logging.info(f"Deploying to machine: {machine_name}")
    execute_command(f'python {DEPLOY_FLEET_PATH} {machine_name}')


def check_and_deploy(fleet_data, defender_devices):
    """Determine machines that require deployment and execute"""
    logging.info("Starting deployment analysis...")
    fleet_hostnames = {machine.get('computer_name') for machine in fleet_data}
    logging.debug(f"Fleet hostnames: {fleet_hostnames}")
    needs_deployment = []

    # Check for Windows devices not in Fleet
    logging.info("Checking for Windows devices not in Fleet...")
    for device in defender_devices:
        hostname = device.get('computerDnsName')
        os_platform = device.get('osPlatform', '')
        
        if hostname not in fleet_hostnames and 'windows' in os_platform.lower():
            logging.debug(f"Windows device {hostname} not found in Fleet, marking for deployment")
            needs_deployment.append(hostname)

    # Check for Fleet machines with incorrect configuration
    logging.info("Checking for Fleet machines with incorrect configuration...")
    for machine in fleet_data:
        hostname = machine.get('computer_name')
        server_url = machine.get('server_url')
        spec_mdm_name = machine.get('spec', {}).get('mdm', {}).get('name')
        
        logging.debug(f"Machine {hostname}: server_url={server_url}, mdm_name={spec_mdm_name}")

        if (
            server_url != "https://fleet.blue-pollux.ts.net/api/mdm/microsoft/discovery" or
            spec_mdm_name != "Fleet"
        ):
            logging.debug(f"Machine {hostname} has incorrect configuration, marking for deployment")
            needs_deployment.append(hostname)

    logging.info(f"Total machines needing deployment: {len(needs_deployment)}")
    logging.debug(f"Machines needing deployment: {needs_deployment}")

    # Deploy to all machines that need it
    for hostname in needs_deployment:
        deploy_to_machine(hostname)


if __name__ == '__main__':
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("complement_deploy_fleet.log"),
            logging.StreamHandler()
        ]
    )
    logging.info("Starting complement_deploy_fleet...")
    from deploy_fleet import LiveResponseClient, load_config

    config = load_config()
    client = LiveResponseClient(
        tenant_id=config['tenant_id'],
        client_id=config['client_id'],
        client_secret=config['client_secret']
    )

    fleet_data = get_fleet_data()
    defender_devices = get_defender_devices(client)

    check_and_deploy(fleet_data, defender_devices)
    logging.info("Deployment routine completed.")

