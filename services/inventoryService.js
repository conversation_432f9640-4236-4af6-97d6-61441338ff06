const DefenderClient = require('../clients/defenderClient');
const FleetClient = require('../clients/fleetClient');
const TailscaleClient = require('../clients/tailscaleClient');
const config = require('./config');

/**
 * Unified Inventory Service
 * Aggregates machine data from Microsoft Defender, Fleet, and Tailscale
 * Creates a unified view with status indicators for each platform
 */
class InventoryService {
  constructor() {
    this.defenderClient = new DefenderClient(config.get('azure'));
    this.fleetClient = new FleetClient(config.get('fleet'));
    this.tailscaleClient = new TailscaleClient(config.get('tailscale'));
    this.cache = {
      data: null,
      timestamp: null,
      ttl: 5 * 60 * 1000 // 5 minutes cache TTL
    };
  }

  /**
   * Normalize hostname for comparison across platforms
   * @param {string} hostname - Original hostname
   * @returns {string} Normalized hostname
   */
  normalizeHostname(hostname) {
    if (!hostname) return '';
    
    let normalized = hostname.toLowerCase().trim();
    
    // Remove common domain suffixes
    const suffixes = ['.local', '.domain', '.corp', '.internal', '.corp.amps.com','.blue-pollux.ts.net','.amps.com','.advancedpricing.com'];
    for (const suffix of suffixes) {
      if (normalized.endsWith(suffix)) {
        normalized = normalized.slice(0, -suffix.length);
        break;
      }
    }
    
    // Remove Tailscale domain suffixes
    if (normalized.includes('.ts.net')) {
      normalized = normalized.split('.')[0];
    }
    
    return normalized;
  }

  /**
   * Fetch data from all platforms
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise<Object>} Aggregated inventory data
   */
  async getInventory(forceRefresh = false) {
    // Check cache
    if (!forceRefresh && this.cache.data && this.cache.timestamp && 
        (Date.now() - this.cache.timestamp) < this.cache.ttl) {
      console.log('✓ Returning cached inventory data');
      return this.cache.data;
    }

    console.log('🔄 Fetching fresh inventory data from all platforms...');
    
    const results = {
      defender: { machines: [], error: null, status: 'unknown' },
      fleet: { hosts: [], error: null, status: 'unknown' },
      tailscale: { devices: [], error: null, status: 'unknown' },
      unified: [],
      summary: {},
      lastUpdated: new Date().toISOString()
    };

    // Fetch from Microsoft Defender
    try {
      results.defender.machines = await this.defenderClient.getMachines();
      results.defender.status = 'success';
      console.log(`✓ Defender: ${results.defender.machines.length} machines`);
    } catch (error) {
      results.defender.error = error.message;
      results.defender.status = 'error';
      console.error('✗ Defender fetch failed:', error.message);
    }

    // Fetch from Fleet
    try {
      results.fleet.hosts = await this.fleetClient.getHosts();
      results.fleet.status = 'success';
      console.log(`✓ Fleet: ${results.fleet.hosts.length} hosts`);
    } catch (error) {
      results.fleet.error = error.message;
      results.fleet.status = 'error';
      console.error('✗ Fleet fetch failed:', error.message);
    }

    // Fetch from Tailscale
    try {
      results.tailscale.devices = await this.tailscaleClient.getDevices();
      results.tailscale.status = 'success';
      console.log(`✓ Tailscale: ${results.tailscale.devices.length} devices`);
    } catch (error) {
      results.tailscale.error = error.message;
      results.tailscale.status = 'error';
      console.error('✗ Tailscale fetch failed:', error.message);
    }

    // Create unified view
    results.unified = this.createUnifiedView(
      results.defender.machines,
      results.fleet.hosts,
      results.tailscale.devices
    );

    // Generate summary
    results.summary = this.generateSummary(results);

    // Cache the results
    this.cache.data = results;
    this.cache.timestamp = Date.now();

    console.log(`✓ Inventory aggregation complete: ${results.unified.length} unique machines`);
    return results;
  }

  /**
   * Create unified view of all machines
   * @param {Array} defenderMachines - Machines from Defender
   * @param {Array} fleetHosts - Hosts from Fleet
   * @param {Array} tailscaleDevices - Devices from Tailscale
   * @returns {Array} Unified machine list
   */
  createUnifiedView(defenderMachines, fleetHosts, tailscaleDevices) {
    const machineMap = new Map();

    // Process Defender machines
    defenderMachines.forEach(machine => {
      const hostname = this.normalizeHostname(machine.hostname);
      if (hostname) {
        machineMap.set(hostname, {
          hostname: hostname,
          originalHostnames: {
            defender: machine.hostname
          },
          defender: {
            present: true,
            data: machine
          },
          fleet: {
            present: false,
            data: null
          },
          tailscale: {
            present: false,
            data: null
          },
          allPlatforms: false
        });
      }
    });

    // Process Fleet hosts
    fleetHosts.forEach(host => {
      const hostname = this.normalizeHostname(host.hostname || host.computerName);
      if (hostname) {
        if (machineMap.has(hostname)) {
          const machine = machineMap.get(hostname);
          machine.fleet.present = true;
          machine.fleet.data = host;
          machine.originalHostnames.fleet = host.hostname || host.computerName;
        } else {
          machineMap.set(hostname, {
            hostname: hostname,
            originalHostnames: {
              fleet: host.hostname || host.computerName
            },
            defender: {
              present: false,
              data: null
            },
            fleet: {
              present: true,
              data: host
            },
            tailscale: {
              present: false,
              data: null
            },
            allPlatforms: false
          });
        }
      }
    });

    // Process Tailscale devices
    tailscaleDevices.forEach(device => {
      const hostname = this.normalizeHostname(device.hostname || device.name);
      if (hostname) {
        if (machineMap.has(hostname)) {
          const machine = machineMap.get(hostname);
          machine.tailscale.present = true;
          machine.tailscale.data = device;
          machine.originalHostnames.tailscale = device.hostname || device.name;
        } else {
          machineMap.set(hostname, {
            hostname: hostname,
            originalHostnames: {
              tailscale: device.hostname || device.name
            },
            defender: {
              present: false,
              data: null
            },
            fleet: {
              present: false,
              data: null
            },
            tailscale: {
              present: true,
              data: device
            },
            allPlatforms: false
          });
        }
      }
    });

    // Calculate allPlatforms status and convert to array
    const unifiedMachines = Array.from(machineMap.values()).map(machine => {
      machine.allPlatforms = machine.defender.present && 
                            machine.fleet.present && 
                            machine.tailscale.present;
      return machine;
    });

    // Sort by hostname
    return unifiedMachines.sort((a, b) => a.hostname.localeCompare(b.hostname));
  }

  /**
   * Generate summary statistics
   * @param {Object} results - Raw results from all platforms
   * @returns {Object} Summary statistics
   */
  generateSummary(results) {
    const summary = {
      platforms: {
        defender: {
          total: results.defender.machines.length,
          status: results.defender.status,
          error: results.defender.error
        },
        fleet: {
          total: results.fleet.hosts.length,
          status: results.fleet.status,
          error: results.fleet.error
        },
        tailscale: {
          total: results.tailscale.devices.length,
          status: results.tailscale.status,
          error: results.tailscale.error
        }
      },
      unified: {
        totalUniqueMachines: results.unified.length,
        inAllPlatforms: results.unified.filter(m => m.allPlatforms).length,
        inDefenderOnly: results.unified.filter(m => m.defender.present && !m.fleet.present && !m.tailscale.present).length,
        inFleetOnly: results.unified.filter(m => !m.defender.present && m.fleet.present && !m.tailscale.present).length,
        inTailscaleOnly: results.unified.filter(m => !m.defender.present && !m.fleet.present && m.tailscale.present).length,
        missingFromDefender: results.unified.filter(m => !m.defender.present).length,
        missingFromFleet: results.unified.filter(m => !m.fleet.present).length,
        missingFromTailscale: results.unified.filter(m => !m.tailscale.present).length
      }
    };

    return summary;
  }

  /**
   * Get health status of all platforms
   * @returns {Promise<Object>} Health status
   */
  async getHealthStatus() {
    const health = {
      overall: 'unknown',
      platforms: {},
      timestamp: new Date().toISOString()
    };

    try {
      // Check each platform
      const [defenderHealth, fleetHealth, tailscaleHealth] = await Promise.allSettled([
        this.defenderClient.getHealthStatus(),
        this.fleetClient.getHealthStatus(),
        this.tailscaleClient.getHealthStatus()
      ]);

      health.platforms.defender = defenderHealth.status === 'fulfilled' ? 
        defenderHealth.value : { status: 'error', message: defenderHealth.reason.message };
      
      health.platforms.fleet = fleetHealth.status === 'fulfilled' ? 
        fleetHealth.value : { status: 'error', message: fleetHealth.reason.message };
      
      health.platforms.tailscale = tailscaleHealth.status === 'fulfilled' ? 
        tailscaleHealth.value : { status: 'error', message: tailscaleHealth.reason.message };

      // Determine overall health
      const healthyCount = Object.values(health.platforms)
        .filter(p => p.status === 'healthy').length;
      
      if (healthyCount === 3) {
        health.overall = 'healthy';
      } else if (healthyCount > 0) {
        health.overall = 'partial';
      } else {
        health.overall = 'error';
      }

    } catch (error) {
      health.overall = 'error';
      health.error = error.message;
    }

    return health;
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.data = null;
    this.cache.timestamp = null;
    console.log('✓ Inventory cache cleared');
  }
}

module.exports = new InventoryService();
