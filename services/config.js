const fs = require('fs');
const path = require('path');

/**
 * Configuration management service
 * Loads configuration from environment variables or config.json file
 * Following the same pattern as the Python deploy_fleet.py script
 */
class ConfigService {
  constructor() {
    this.config = null;
    this.loadConfig();
  }

  loadConfig() {
    // Start with environment variables
    this.config = {
      // Microsoft Defender credentials
      azure: {
        tenant_id: process.env.AZURE_TENANT_ID,
        client_id: process.env.AZURE_CLIENT_ID,
        client_secret: process.env.AZURE_CLIENT_SECRET
      },
      
      // Fleet credentials
      fleet: {
        url: process.env.FLEET_URL || 'https://fleet.blue-pollux.ts.net',
        api_token: process.env.FLEET_API_TOKEN,
        username: process.env.FLEET_USERNAME,
        password: process.env.FLEET_PASSWORD
      },
      
      // Tailscale credentials
      tailscale: {
        api_key: process.env.TAILSCALE_API_KEY,
        tailnet: process.env.TAILSCALE_TAILNET
      },
      
      // Server configuration
      server: {
        port: process.env.PORT || 3000,
        node_env: process.env.NODE_ENV || 'development'
      }
    };

    // Try to load from config.json if environment variables are not complete
    if (!this.isConfigComplete()) {
      this.loadFromFile();
    }
  }

  loadFromFile() {
    const configPath = path.join(process.cwd(), 'config.json');
    
    try {
      if (fs.existsSync(configPath)) {
        const fileContent = fs.readFileSync(configPath, 'utf8');
        const fileConfig = JSON.parse(fileContent);
        
        // Merge file config with existing config, prioritizing environment variables
        this.config = this.mergeConfigs(this.config, fileConfig);
        
        console.log('✓ Configuration loaded from config.json');
      }
    } catch (error) {
      console.warn('⚠ Failed to load config.json:', error.message);
    }
  }

  mergeConfigs(envConfig, fileConfig) {
    const merged = { ...envConfig };
    
    // Merge Azure config
    if (fileConfig.azure) {
      merged.azure = {
        tenant_id: envConfig.azure.tenant_id || fileConfig.azure.tenant_id || fileConfig.tenant_id,
        client_id: envConfig.azure.client_id || fileConfig.azure.client_id || fileConfig.client_id,
        client_secret: envConfig.azure.client_secret || fileConfig.azure.client_secret || fileConfig.client_secret
      };
    }
    
    // Merge Fleet config
    if (fileConfig.fleet) {
      merged.fleet = {
        url: envConfig.fleet.url || fileConfig.fleet.url,
        api_token: envConfig.fleet.api_token || fileConfig.fleet.api_token,
        username: envConfig.fleet.username || fileConfig.fleet.username,
        password: envConfig.fleet.password || fileConfig.fleet.password
      };
    }
    
    // Merge Tailscale config
    if (fileConfig.tailscale) {
      merged.tailscale = {
        api_key: envConfig.tailscale.api_key || fileConfig.tailscale.api_key,
        tailnet: envConfig.tailscale.tailnet || fileConfig.tailscale.tailnet
      };
    }
    
    return merged;
  }

  isConfigComplete() {
    const azure = this.config.azure;
    const fleet = this.config.fleet;
    const tailscale = this.config.tailscale;
    
    const azureComplete = azure.tenant_id && azure.client_id && azure.client_secret;
    const fleetComplete = fleet.url && (fleet.api_token || (fleet.username && fleet.password));
    const tailscaleComplete = tailscale.api_key && tailscale.tailnet;
    
    return azureComplete && fleetComplete && tailscaleComplete;
  }

  validateConfig() {
    const missing = [];
    
    // Check Azure config
    if (!this.config.azure.tenant_id) missing.push('AZURE_TENANT_ID');
    if (!this.config.azure.client_id) missing.push('AZURE_CLIENT_ID');
    if (!this.config.azure.client_secret) missing.push('AZURE_CLIENT_SECRET');
    
    // Check Fleet config
    if (!this.config.fleet.url) missing.push('FLEET_URL');
    if (!this.config.fleet.api_token && (!this.config.fleet.username || !this.config.fleet.password)) {
      missing.push('FLEET_API_TOKEN or (FLEET_USERNAME and FLEET_PASSWORD)');
    }
    
    // Check Tailscale config
    if (!this.config.tailscale.api_key) missing.push('TAILSCALE_API_KEY');
    if (!this.config.tailscale.tailnet) missing.push('TAILSCALE_TAILNET');
    
    if (missing.length > 0) {
      console.error('✗ Missing required configuration:');
      missing.forEach(key => {
        console.error(`  - ${key}: Set environment variable or add to config.json`);
      });
      
      console.error('\nExample config.json:');
      console.error(JSON.stringify({
        "azure": {
          "tenant_id": "your-tenant-id",
          "client_id": "your-client-id",
          "client_secret": "your-client-secret"
        },
        "fleet": {
          "url": "https://fleet.blue-pollux.ts.net",
          "api_token": "your-fleet-api-token"
        },
        "tailscale": {
          "api_key": "your-tailscale-api-key",
          "tailnet": "your-tailnet"
        }
      }, null, 2));
      
      return false;
    }
    
    return true;
  }

  get(section, key = null) {
    if (key) {
      return this.config[section]?.[key];
    }
    return this.config[section];
  }

  getAll() {
    return this.config;
  }
}

// Export singleton instance
module.exports = new ConfigService();
