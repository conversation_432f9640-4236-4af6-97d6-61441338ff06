const express = require('express');
const router = express.Router();
const inventoryService = require('../services/inventoryService');
const config = require('../services/config');

/**
 * GET /api/inventory
 * Get unified machine inventory from all platforms
 */
router.get('/', async (req, res) => {
  try {
    const forceRefresh = req.query.refresh === 'true';
    const inventory = await inventoryService.getInventory(forceRefresh);
    
    res.json({
      success: true,
      data: inventory,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching inventory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch inventory data',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/inventory/summary
 * Get summary statistics only
 */
router.get('/summary', async (req, res) => {
  try {
    const inventory = await inventoryService.getInventory();
    
    res.json({
      success: true,
      data: inventory.summary,
      lastUpdated: inventory.lastUpdated,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching inventory summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch inventory summary',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/inventory/unified
 * Get only the unified machine list
 */
router.get('/unified', async (req, res) => {
  try {
    const forceRefresh = req.query.refresh === 'true';
    const inventory = await inventoryService.getInventory(forceRefresh);
    
    res.json({
      success: true,
      data: inventory.unified,
      summary: inventory.summary,
      lastUpdated: inventory.lastUpdated,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching unified inventory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch unified inventory',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/inventory/platforms/:platform
 * Get data from a specific platform
 */
router.get('/platforms/:platform', async (req, res) => {
  try {
    const platform = req.params.platform.toLowerCase();
    const validPlatforms = ['defender', 'fleet', 'tailscale'];
    
    if (!validPlatforms.includes(platform)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid platform',
        message: `Platform must be one of: ${validPlatforms.join(', ')}`,
        timestamp: new Date().toISOString()
      });
    }

    const forceRefresh = req.query.refresh === 'true';
    const inventory = await inventoryService.getInventory(forceRefresh);
    
    res.json({
      success: true,
      data: inventory[platform],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`Error fetching ${req.params.platform} data:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to fetch ${req.params.platform} data`,
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/inventory/refresh
 * Force refresh of inventory data
 */
router.post('/refresh', async (req, res) => {
  try {
    console.log('🔄 Manual inventory refresh requested');
    inventoryService.clearCache();
    const inventory = await inventoryService.getInventory(true);
    
    res.json({
      success: true,
      message: 'Inventory data refreshed successfully',
      data: inventory,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error refreshing inventory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to refresh inventory data',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/inventory/health
 * Get health status of all platforms
 */
router.get('/health', async (req, res) => {
  try {
    const health = await inventoryService.getHealthStatus();
    
    const statusCode = health.overall === 'healthy' ? 200 : 
                      health.overall === 'partial' ? 207 : 503;
    
    res.status(statusCode).json({
      success: health.overall !== 'error',
      data: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error checking health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check platform health',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/inventory/search
 * Search for machines by hostname
 */
router.get('/search', async (req, res) => {
  try {
    const query = req.query.q;
    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required',
        message: 'Provide a search query using the "q" parameter',
        timestamp: new Date().toISOString()
      });
    }

    const inventory = await inventoryService.getInventory();
    const normalizedQuery = inventoryService.normalizeHostname(query);
    
    const results = inventory.unified.filter(machine => 
      machine.hostname.includes(normalizedQuery) ||
      Object.values(machine.originalHostnames).some(hostname => 
        hostname && hostname.toLowerCase().includes(query.toLowerCase())
      )
    );
    
    res.json({
      success: true,
      data: results,
      query: query,
      count: results.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error searching inventory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search inventory',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/inventory/config
 * Get configuration status (without sensitive data)
 */
router.get('/config', (req, res) => {
  try {
    const configStatus = {
      azure: {
        configured: !!(config.get('azure', 'tenant_id') && 
                      config.get('azure', 'client_id') && 
                      config.get('azure', 'client_secret'))
      },
      fleet: {
        configured: !!(config.get('fleet', 'url') && 
                      (config.get('fleet', 'api_token') || 
                       (config.get('fleet', 'username') && config.get('fleet', 'password'))))
      },
      tailscale: {
        configured: !!(config.get('tailscale', 'api_key') && 
                      config.get('tailscale', 'tailnet'))
      }
    };

    const allConfigured = Object.values(configStatus).every(platform => platform.configured);

    res.json({
      success: true,
      data: {
        platforms: configStatus,
        allConfigured: allConfigured,
        configValid: config.validateConfig()
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error checking config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check configuration',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
