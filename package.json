{"name": "security-platform-dashboard", "version": "1.0.0", "description": "Web dashboard for unified machine inventory across Microsoft Defender, Fleet, and Tailscale", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "demo": "node demo-with-mock-data.js", "test": "node test-api.js"}, "keywords": ["security", "dashboard", "microsoft-defender", "fleet", "tailscale", "inventory"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}}