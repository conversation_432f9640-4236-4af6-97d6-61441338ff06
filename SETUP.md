# Security Platform Dashboard Setup Guide

## Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure API credentials** (see Configuration section below)

3. **Start the server**:
   ```bash
   npm start
   ```

4. **Open the dashboard**:
   ```
   http://localhost:3000
   ```

## Configuration

### Method 1: Environment Variables (Recommended for Production)

```bash
# Microsoft Defender for Endpoint
export AZURE_TENANT_ID="your-azure-tenant-id"
export AZURE_CLIENT_ID="your-azure-client-id"
export AZURE_CLIENT_SECRET="your-azure-client-secret"

# Fleet
export FLEET_URL="https://fleet.blue-pollux.ts.net"
export FLEET_API_TOKEN="your-fleet-api-token"

# Tailscale
export TAILSCALE_API_KEY="your-tailscale-api-key"
export TAILSCALE_TAILNET="your-tailnet-name"

# Start the server
npm start
```

### Method 2: Configuration File

```bash
# Copy the example config
cp config.json.example config.json

# Edit config.json with your credentials
{
  "azure": {
    "tenant_id": "your-azure-tenant-id",
    "client_id": "your-azure-client-id",
    "client_secret": "your-azure-client-secret"
  },
  "fleet": {
    "url": "https://fleet.blue-pollux.ts.net",
    "api_token": "your-fleet-api-token"
  },
  "tailscale": {
    "api_key": "your-tailscale-api-key",
    "tailnet": "your-tailnet-name"
  }
}
```

### Method 3: .env File

```bash
# Copy the example .env
cp .env.example .env

# Edit .env with your credentials
AZURE_TENANT_ID=your-azure-tenant-id
AZURE_CLIENT_ID=your-azure-client-id
AZURE_CLIENT_SECRET=your-azure-client-secret
FLEET_URL=https://fleet.blue-pollux.ts.net
FLEET_API_TOKEN=your-fleet-api-token
TAILSCALE_API_KEY=your-tailscale-api-key
TAILSCALE_TAILNET=your-tailnet-name
```

## Getting API Credentials

### Microsoft Defender for Endpoint

1. **Register Azure AD Application**:
   - Go to Azure Portal → Azure Active Directory → App registrations
   - Click "New registration"
   - Name: "Security Platform Dashboard"
   - Supported account types: "Accounts in this organizational directory only"
   - Click "Register"

2. **Configure API Permissions**:
   - Go to "API permissions"
   - Click "Add a permission"
   - Select "APIs my organization uses"
   - Search for "WindowsDefenderATP"
   - Select "Application permissions"
   - Add: `Machine.Read.All`
   - Click "Grant admin consent"

3. **Create Client Secret**:
   - Go to "Certificates & secrets"
   - Click "New client secret"
   - Description: "Dashboard API Access"
   - Expires: Choose appropriate duration
   - Copy the secret value (you won't see it again!)

4. **Get Tenant and Client IDs**:
   - Tenant ID: Available on the app overview page
   - Client ID: Available on the app overview page

### Fleet

**Option A: API Token (Recommended)**
1. Log into Fleet admin panel
2. Go to Settings → Integrations → API
3. Generate a new API token
4. Copy the token

**Option B: Username/Password**
1. Use your Fleet admin username and password
2. Set `FLEET_USERNAME` and `FLEET_PASSWORD` instead of `FLEET_API_TOKEN`

### Tailscale

1. **Generate API Key**:
   - Go to Tailscale admin console
   - Settings → Keys
   - Generate API key
   - Copy the key

2. **Get Tailnet Name**:
   - Your tailnet name is visible in the admin console
   - Example: if your tailnet is `example.ts.net`, use `example`

## Testing the Setup

1. **Check Configuration**:
   ```bash
   curl http://localhost:3000/api/inventory/config
   ```

2. **Check Platform Health**:
   ```bash
   curl http://localhost:3000/api/inventory/health
   ```

3. **Test Data Retrieval**:
   ```bash
   curl http://localhost:3000/api/inventory/unified
   ```

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify API credentials are correct
   - Check API permissions (especially for Defender)
   - Ensure client secret hasn't expired

2. **Network Issues**:
   - Verify URLs are accessible
   - Check firewall settings
   - Ensure proper DNS resolution

3. **Permission Issues**:
   - Defender: Ensure `Machine.Read.All` permission is granted
   - Fleet: Ensure API token has read access
   - Tailscale: Ensure API key has device read permissions

### Debug Mode

Start the server with debug logging:
```bash
NODE_ENV=development npm start
```

### API Testing

Test individual platform endpoints:
```bash
# Test Defender
curl http://localhost:3000/api/inventory/platforms/defender

# Test Fleet
curl http://localhost:3000/api/inventory/platforms/fleet

# Test Tailscale
curl http://localhost:3000/api/inventory/platforms/tailscale
```

## Security Best Practices

1. **Use Environment Variables in Production**
2. **Store secrets in secure key management systems**
3. **Use HTTPS in production**
4. **Regularly rotate API keys and secrets**
5. **Limit API permissions to minimum required**
6. **Monitor API usage and access logs**

## Production Deployment

1. **Use a process manager** (PM2, systemd, etc.)
2. **Set up reverse proxy** (nginx, Apache)
3. **Configure HTTPS** with proper certificates
4. **Set up monitoring** and logging
5. **Configure firewall** rules
6. **Use environment variables** for all secrets
