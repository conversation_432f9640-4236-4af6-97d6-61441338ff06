# Microsoft Defender for Endpoint API credentials
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret

# Fleet API credentials
FLEET_URL=https://fleet.blue-pollux.ts.net
FLEET_API_TOKEN=your-fleet-api-token
# Alternative: username/password authentication
# FLEET_USERNAME=your-fleet-username
# FLEET_PASSWORD=your-fleet-password

# Tailscale API credentials
TAILSCALE_API_KEY=your-tailscale-api-key
TAILSCALE_TAILNET=your-tailnet

# Server configuration
PORT=3000
NODE_ENV=development
