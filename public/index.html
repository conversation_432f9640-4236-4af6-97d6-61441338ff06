<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Platform Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🛡️ Security Platform Dashboard</h1>
            <p class="subtitle">Unified machine inventory across Microsoft Defender, Fleet, and Tailscale</p>
            <div class="header-controls">
                <button id="refreshBtn" class="btn btn-primary">
                    <span class="icon">🔄</span> Refresh Data
                </button>
                <button id="healthBtn" class="btn btn-secondary">
                    <span class="icon">❤️</span> Health Check
                </button>
            </div>
        </header>

        <div class="status-bar" id="statusBar">
            <div class="status-item">
                <span class="status-label">Last Updated:</span>
                <span id="lastUpdated">Loading...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Total Machines:</span>
                <span id="totalMachines">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">All Platforms:</span>
                <span id="allPlatforms">-</span>
            </div>
        </div>

        <div class="controls">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search machines by hostname..." class="search-input">
                <button id="clearSearch" class="btn btn-small">Clear</button>
            </div>
            <div class="filter-container">
                <label>
                    <input type="checkbox" id="filterAllPlatforms"> Show only machines in all platforms
                </label>
                <label>
                    <input type="checkbox" id="filterMissing"> Show only machines missing from platforms
                </label>
            </div>
        </div>

        <div class="summary-cards" id="summaryCards">
            <!-- Summary cards will be populated by JavaScript -->
        </div>

        <div class="table-container">
            <table class="inventory-table" id="inventoryTable">
                <thead>
                    <tr>
                        <th class="hostname-col">Hostname</th>
                        <th class="platform-col">
                            <div class="platform-header">
                                <span class="platform-icon">🛡️</span>
                                <span>Defender</span>
                            </div>
                        </th>
                        <th class="platform-col">
                            <div class="platform-header">
                                <span class="platform-icon">🚢</span>
                                <span>Fleet</span>
                            </div>
                        </th>
                        <th class="platform-col">
                            <div class="platform-header">
                                <span class="platform-icon">🔗</span>
                                <span>Tailscale</span>
                            </div>
                        </th>
                        <th class="platform-col">
                            <div class="platform-header">
                                <span class="platform-icon">✅</span>
                                <span>All Platforms</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody id="inventoryTableBody">
                    <!-- Table rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <p>Loading inventory data...</p>
        </div>

        <div class="error-message" id="errorMessage" style="display: none;">
            <div class="error-content">
                <h3>⚠️ Error Loading Data</h3>
                <p id="errorText"></p>
                <button id="retryBtn" class="btn btn-primary">Retry</button>
            </div>
        </div>
    </div>

    <!-- Health Status Modal -->
    <div class="modal" id="healthModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Platform Health Status</h2>
                <button class="modal-close" id="closeHealthModal">&times;</button>
            </div>
            <div class="modal-body" id="healthModalBody">
                <!-- Health status will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 Security Platform Dashboard | Built with ❤️ for unified security monitoring</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
