/* Security Platform Dashboard Styles */

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: var(--surface-color);
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.header-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #475569;
    transform: translateY(-1px);
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status Bar */
.status-bar {
    display: flex;
    gap: 30px;
    background: var(--surface-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.status-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-item span:last-child {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Controls */
.controls {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.search-container {
    display: flex;
    gap: 10px;
    align-items: center;
    flex: 1;
    min-width: 300px;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-container {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-container label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    cursor: pointer;
}

.filter-container input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: var(--surface-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    text-align: center;
}

.summary-card h3 {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-card .number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.summary-card.success .number {
    color: var(--success-color);
}

.summary-card.error .number {
    color: var(--error-color);
}

.summary-card.warning .number {
    color: var(--warning-color);
}

/* Table Styles */
.table-container {
    background: var(--surface-color);
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.inventory-table {
    width: 100%;
    border-collapse: collapse;
}

.inventory-table th {
    background-color: #f1f5f9;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
}

.inventory-table td {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.inventory-table tbody tr:hover {
    background-color: #f8fafc;
}

.hostname-col {
    width: 30%;
    min-width: 200px;
}

.platform-col {
    width: 17.5%;
    text-align: center;
}

.platform-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.platform-icon {
    font-size: 18px;
}

.hostname {
    font-weight: 600;
    color: var(--text-primary);
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-size: 18px;
    font-weight: bold;
}

.status-present {
    background-color: #dcfce7;
    color: var(--success-color);
}

.status-missing {
    background-color: #fee2e2;
    color: var(--error-color);
}

/* Loading and Error States */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: var(--surface-color);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background: var(--surface-color);
    border: 2px solid var(--error-color);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    margin-bottom: 30px;
}

.error-content h3 {
    color: var(--error-color);
    margin-bottom: 15px;
}

.error-content p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--surface-color);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
}

.health-status {
    display: grid;
    gap: 15px;
}

.health-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.health-item.healthy {
    border-color: var(--success-color);
    background-color: #f0fdf4;
}

.health-item.error {
    border-color: var(--error-color);
    background-color: #fef2f2;
}

.health-item.partial {
    border-color: var(--warning-color);
    background-color: #fffbeb;
}

.health-platform {
    font-weight: 600;
    color: var(--text-primary);
}

.health-status-text {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.health-status-text.healthy {
    color: var(--success-color);
}

.health-status-text.error {
    color: var(--error-color);
}

.health-status-text.partial {
    color: var(--warning-color);
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .header-controls {
        flex-direction: column;
        align-items: center;
    }

    .status-bar {
        flex-direction: column;
        gap: 15px;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-container {
        min-width: auto;
    }

    .filter-container {
        justify-content: center;
    }

    .inventory-table {
        font-size: 14px;
    }

    .inventory-table th,
    .inventory-table td {
        padding: 12px 8px;
    }

    .platform-header span:last-child {
        display: none;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
