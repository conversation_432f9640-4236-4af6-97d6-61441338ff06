// Security Platform Dashboard JavaScript

class Dashboard {
    constructor() {
        this.data = null;
        this.filteredData = null;
        this.isLoading = false;
        this.searchTerm = '';
        this.filters = {
            allPlatforms: false,
            missing: false
        };
        
        this.initializeEventListeners();
        this.loadData();
    }

    initializeEventListeners() {
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshData();
        });

        // Health check button
        document.getElementById('healthBtn').addEventListener('click', () => {
            this.showHealthStatus();
        });

        // Search input
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.applyFilters();
        });

        // Clear search button
        document.getElementById('clearSearch').addEventListener('click', () => {
            document.getElementById('searchInput').value = '';
            this.searchTerm = '';
            this.applyFilters();
        });

        // Filter checkboxes
        document.getElementById('filterAllPlatforms').addEventListener('change', (e) => {
            this.filters.allPlatforms = e.target.checked;
            this.applyFilters();
        });

        document.getElementById('filterMissing').addEventListener('change', (e) => {
            this.filters.missing = e.target.checked;
            this.applyFilters();
        });

        // Modal close
        document.getElementById('closeHealthModal').addEventListener('click', () => {
            this.hideHealthModal();
        });

        // Close modal on background click
        document.getElementById('healthModal').addEventListener('click', (e) => {
            if (e.target.id === 'healthModal') {
                this.hideHealthModal();
            }
        });

        // Retry button
        document.getElementById('retryBtn').addEventListener('click', () => {
            this.loadData();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideHealthModal();
            }
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.refreshData();
            }
        });
    }

    async loadData() {
        this.showLoading();
        this.hideError();

        try {
            const response = await fetch('/api/inventory/unified');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || 'Failed to load data');
            }

            this.data = result.data;
            this.updateSummary(result.summary);
            this.updateLastUpdated(result.lastUpdated);
            this.applyFilters();
            this.hideLoading();

        } catch (error) {
            console.error('Error loading data:', error);
            this.showError(error.message);
            this.hideLoading();
        }
    }

    async refreshData() {
        if (this.isLoading) return;

        const refreshBtn = document.getElementById('refreshBtn');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<span class="icon">⏳</span> Refreshing...';
        refreshBtn.disabled = true;

        try {
            const response = await fetch('/api/inventory/unified?refresh=true');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || 'Failed to refresh data');
            }

            this.data = result.data;
            this.updateSummary(result.summary);
            this.updateLastUpdated(result.lastUpdated);
            this.applyFilters();
            this.hideError();

        } catch (error) {
            console.error('Error refreshing data:', error);
            this.showError(error.message);
        } finally {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }
    }

    applyFilters() {
        if (!this.data) return;

        let filtered = [...this.data];

        // Apply search filter
        if (this.searchTerm) {
            filtered = filtered.filter(machine => 
                machine.hostname.toLowerCase().includes(this.searchTerm) ||
                Object.values(machine.originalHostnames).some(hostname => 
                    hostname && hostname.toLowerCase().includes(this.searchTerm)
                )
            );
        }

        // Apply platform filters
        if (this.filters.allPlatforms) {
            filtered = filtered.filter(machine => machine.allPlatforms);
        }

        if (this.filters.missing) {
            filtered = filtered.filter(machine => 
                !machine.defender.present || 
                !machine.fleet.present || 
                !machine.tailscale.present
            );
        }

        this.filteredData = filtered;
        this.renderTable();
    }

    renderTable() {
        const tbody = document.getElementById('inventoryTableBody');
        
        if (!this.filteredData || this.filteredData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                        ${this.data && this.data.length === 0 ? 
                            'No machines found in any platform.' : 
                            'No machines match the current filters.'}
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.filteredData.map(machine => `
            <tr>
                <td>
                    <div class="hostname">${this.escapeHtml(machine.hostname)}</div>
                </td>
                <td style="text-align: center;">
                    ${this.renderStatusIndicator(machine.defender.present)}
                </td>
                <td style="text-align: center;">
                    ${this.renderStatusIndicator(machine.fleet.present)}
                </td>
                <td style="text-align: center;">
                    ${this.renderStatusIndicator(machine.tailscale.present)}
                </td>
                <td style="text-align: center;">
                    ${this.renderStatusIndicator(machine.allPlatforms)}
                </td>
            </tr>
        `).join('');
    }

    renderStatusIndicator(isPresent) {
        return `
            <div class="status-indicator ${isPresent ? 'status-present' : 'status-missing'}">
                ${isPresent ? '✓' : '✗'}
            </div>
        `;
    }

    updateSummary(summary) {
        if (!summary) return;

        const summaryCards = document.getElementById('summaryCards');
        summaryCards.innerHTML = `
            <div class="summary-card">
                <h3>Total Machines</h3>
                <div class="number">${summary.unified.totalUniqueMachines}</div>
            </div>
            <div class="summary-card success">
                <h3>All Platforms</h3>
                <div class="number">${summary.unified.inAllPlatforms}</div>
            </div>
            <div class="summary-card error">
                <h3>Missing from Defender</h3>
                <div class="number">${summary.unified.missingFromDefender}</div>
            </div>
            <div class="summary-card error">
                <h3>Missing from Fleet</h3>
                <div class="number">${summary.unified.missingFromFleet}</div>
            </div>
            <div class="summary-card error">
                <h3>Missing from Tailscale</h3>
                <div class="number">${summary.unified.missingFromTailscale}</div>
            </div>
        `;

        // Update status bar
        document.getElementById('totalMachines').textContent = summary.unified.totalUniqueMachines;
        document.getElementById('allPlatforms').textContent = summary.unified.inAllPlatforms;
    }

    updateLastUpdated(lastUpdated) {
        if (!lastUpdated) return;
        
        const date = new Date(lastUpdated);
        const formatted = date.toLocaleString();
        document.getElementById('lastUpdated').textContent = formatted;
    }

    async showHealthStatus() {
        try {
            const response = await fetch('/api/inventory/health');
            const result = await response.json();
            
            const healthModalBody = document.getElementById('healthModalBody');
            healthModalBody.innerHTML = this.renderHealthStatus(result.data);
            
            document.getElementById('healthModal').style.display = 'flex';
        } catch (error) {
            console.error('Error fetching health status:', error);
            alert('Failed to fetch health status: ' + error.message);
        }
    }

    renderHealthStatus(health) {
        if (!health) return '<p>No health data available</p>';

        const overallClass = health.overall === 'healthy' ? 'healthy' : 
                           health.overall === 'partial' ? 'partial' : 'error';

        return `
            <div class="health-status">
                <div class="health-item ${overallClass}">
                    <div class="health-platform">Overall Status</div>
                    <div class="health-status-text ${overallClass}">
                        ${health.overall === 'healthy' ? '✅ All Systems Operational' :
                          health.overall === 'partial' ? '⚠️ Some Issues Detected' :
                          '❌ System Errors'}
                    </div>
                </div>
                
                ${Object.entries(health.platforms).map(([platform, status]) => {
                    const statusClass = status.status === 'healthy' ? 'healthy' : 'error';
                    const platformName = platform.charAt(0).toUpperCase() + platform.slice(1);
                    
                    return `
                        <div class="health-item ${statusClass}">
                            <div class="health-platform">${platformName}</div>
                            <div class="health-status-text ${statusClass}">
                                ${status.status === 'healthy' ? '✅' : '❌'} ${status.message}
                            </div>
                        </div>
                    `;
                }).join('')}
                
                <div style="margin-top: 15px; font-size: 12px; color: var(--text-secondary);">
                    Last checked: ${new Date(health.timestamp).toLocaleString()}
                </div>
            </div>
        `;
    }

    hideHealthModal() {
        document.getElementById('healthModal').style.display = 'none';
    }

    showLoading() {
        this.isLoading = true;
        document.getElementById('loadingIndicator').style.display = 'flex';
        document.getElementById('inventoryTable').style.display = 'none';
    }

    hideLoading() {
        this.isLoading = false;
        document.getElementById('loadingIndicator').style.display = 'none';
        document.getElementById('inventoryTable').style.display = 'table';
    }

    showError(message) {
        document.getElementById('errorText').textContent = message;
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('inventoryTable').style.display = 'none';
    }

    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});
